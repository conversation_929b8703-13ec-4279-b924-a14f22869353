# Monorepo Backend CI Dockerfile v2
# 全新設計：基於 Monorepo 架構的智能緩存後端映像
# 優化目標：最大化 Docker 層緩存效率，最小化構建時間

# ========================================
# Stage 1: Base Dependencies
# ========================================
FROM python:3.11-slim AS base

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴（這一層很少變化，緩存效果好）
RUN apt-get update && apt-get install -y \
    git \
    gcc \
    g++ \
    libpq-dev \
    libxml2-dev \
    libxslt1-dev \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 升級 pip（這一層也很少變化）
RUN pip install --upgrade pip

# 🐍 確保 Python 與 pip 命令在所有路徑下可用
RUN ln -sf /usr/local/bin/python3 /usr/local/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python \
    && ln -sf /usr/local/bin/python3 /usr/bin/python3 \
    && ln -sf /usr/local/bin/pip3 /usr/local/bin/pip \
    && ln -sf /usr/local/bin/pip3 /usr/bin/pip \
    && ln -sf /usr/local/bin/python3 /usr/local/bin/Python \
    && ln -sf /usr/local/bin/python3 /usr/bin/Python

# ========================================
# Stage 2: Python Dependencies
# ========================================
FROM base AS dependencies

# 複製 requirements.txt（單獨複製以利用 Docker 層緩存）
COPY backend/requirements.txt ./requirements-full.txt

# 創建 CI 專用的精簡版 requirements（移除 crawl4ai 等重型依賴）
RUN grep -v "crawl4ai" requirements-full.txt > requirements-ci.txt && \
    echo "📦 CI Requirements (移除 crawl4ai):" && \
    cat requirements-ci.txt

# 安裝精簡版依賴（這一層在 requirements.txt 不變時會被緩存）
RUN pip install --no-cache-dir -r requirements-ci.txt

# 安裝 Playwright（但只安裝必要的瀏覽器）
RUN playwright install chromium && \
    playwright install-deps chromium

# ========================================
# Stage 3: Application Setup
# ========================================
FROM dependencies AS application

# [FIX] 創建日誌目錄並確保 ci-user 有寫入權限
# 這解決了 Django 啟動時因找不到日誌目錄而引發的 FileNotFoundError
RUN mkdir -p /workspace/backend/logs

# 設置用戶權限
RUN useradd -m -s /bin/bash ci-user && \
    chown -R ci-user:ci-user /workspace

# 設置環境變數
ENV PYTHONPATH=/workspace/backend
ENV DJANGO_SETTINGS_MODULE=config.django_settings
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 複製後端應用程式碼（現在不使用 volume mount，需要將程式碼內建到映像中）
COPY backend/ ./backend/

# [FIX] 複製後重新設置關鍵目錄權限（因為 COPY 會覆蓋權限）
RUN chown -R ci-user:ci-user /workspace/backend/logs && \
    mkdir -p /workspace/backend/staticfiles && \
    chown -R ci-user:ci-user /workspace/backend/staticfiles

# 切換到非 root 用戶
USER ci-user

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python --version || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="2.0-monorepo-ci"
LABEL description="Monorepo Backend CI image with intelligent caching"
LABEL tier="2.0-monorepo-ci"
LABEL optimization="Monorepo-aware with intelligent Docker layer caching"

# 預設命令
CMD ["bash"]
